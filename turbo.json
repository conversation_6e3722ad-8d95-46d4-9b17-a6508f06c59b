{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"]}, "diagram-core#build": {"outputs": ["dist/**"]}, "flowmind-extension#build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "flowmind-extension#dev": {"cache": false, "persistent": true}, "lint": {}, "dev": {"cache": false, "persistent": true}}}