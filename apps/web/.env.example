# AI 模型配置示例
# 复制此文件为 .env.local 并填入您的 API 密钥
# 注意：Next.js 客户端环境变量必须以 NEXT_PUBLIC_ 开头

# 豆包 (火山引擎) 配置
NEXT_PUBLIC_ARK_API_KEY=your-volcengine-api-key
NEXT_PUBLIC_ARK_MODEL_NAME=ep-20250617131345-rshkp
NEXT_PUBLIC_ARK_ENDPOINT=https://ark.cn-beijing.volces.com/api/v3

# OpenAI 配置 (可选)
# NEXT_PUBLIC_OPENAI_API_KEY=your-openai-api-key
# NEXT_PUBLIC_OPENAI_MODEL_NAME=gpt-4

# Claude 配置 (可选)
# NEXT_PUBLIC_ANTHROPIC_API_KEY=your-anthropic-api-key
# NEXT_PUBLIC_ANTHROPIC_MODEL_NAME=claude-3-sonnet-20240229

# Qwen 配置 (可选)
# NEXT_PUBLIC_QWEN_API_KEY=your-qwen-api-key
# NEXT_PUBLIC_QWEN_MODEL_NAME=qwen-max
# NEXT_PUBLIC_QWEN_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1

# 默认配置
NEXT_PUBLIC_DEFAULT_TEMPERATURE=0.7
NEXT_PUBLIC_DEFAULT_MAX_TOKENS=2048