@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
  
  body {
    @apply bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Chrome extension specific styles */
  #root {
    min-height: 100vh;
    width: 100%;
  }

  /* Popup specific styles */
  .popup-container {
    width: 400px;
    min-height: 600px;
    max-height: 800px;
    overflow-y: auto;
  }

  /* Options page specific styles */
  .options-container {
    min-height: 100vh;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Mermaid diagram container */
  .mermaid-container {
    @apply bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles for accessibility */
  .focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
  }

  /* Toast notification styles */
  .toast-container {
    @apply fixed top-4 right-4 z-50 flex flex-col gap-2;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus-visible:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
  }

  .btn-destructive {
    @apply bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500;
  }

  /* Input styles */
  .input-field {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:ring-offset-gray-900 dark:placeholder:text-gray-400;
  }

  /* Card styles */
  .card {
    @apply rounded-lg border bg-white text-gray-900 shadow-sm dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
}

@layer components {
  /* Diagram generator specific styles */
  .diagram-input-panel {
    @apply space-y-4 p-4 bg-white rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700;
  }

  .diagram-preview {
    @apply bg-white dark:bg-gray-900 rounded-lg border overflow-hidden;
  }

  .diagram-toolbar {
    @apply flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 border-b;
  }

  .model-selector {
    @apply w-full max-w-xs;
  }

  .quick-example-btn {
    @apply text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors;
  }

  /* Extension specific components */
  .extension-header {
    @apply flex items-center justify-between p-4 border-b bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700;
  }

  .extension-content {
    @apply flex-1 overflow-y-auto p-4;
  }

  .extension-footer {
    @apply p-4 border-t bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700;
  }

  /* Settings page styles */
  .settings-section {
    @apply space-y-4 p-6 bg-white rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700;
  }

  .settings-section-title {
    @apply text-lg font-semibold mb-4;
  }

  .settings-field {
    @apply space-y-2;
  }

  .settings-label {
    @apply text-sm font-medium;
  }

  /* API key input with toggle visibility */
  .api-key-input-container {
    @apply relative;
  }

  .api-key-toggle {
    @apply absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded;
  }
}

@layer utilities {
  /* Extension specific utilities */
  .popup-width {
    width: 400px;
  }

  .popup-height {
    min-height: 600px;
    max-height: 800px;
  }

  .options-width {
    max-width: 1200px;
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Flex utilities */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  /* Grid utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  /* Transition utilities */
  .transition-base {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  /* Border utilities */
  .border-dashed-primary {
    @apply border-2 border-dashed border-blue-500/30;
  }
}