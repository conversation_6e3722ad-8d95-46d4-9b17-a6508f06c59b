{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "declarationMap": false, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["chrome", "node", "vite/client"]}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "vite.config.ts"], "exclude": ["node_modules", "dist", "build"], "references": [{"path": "../diagram-core"}]}