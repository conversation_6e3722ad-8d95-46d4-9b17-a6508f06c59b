{"name": "@flowmind/mcp-server", "version": "0.1.0", "description": "MCP server for Mermaid diagram generation and validation", "type": "module", "main": "dist/index.js", "bin": {"mcp-mermaid": "dist/index.js"}, "scripts": {"build": "tsup src/index.ts --format esm --target node18 --clean", "dev": "tsup src/index.ts --format esm --target node18 --watch", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch"}, "keywords": ["mcp", "mermaid", "diagram", "ai", "claude"], "author": "FlowMind", "license": "MIT", "dependencies": {"@mermaid-js/parser": "^0.6.1", "@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.24", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^1.0.0"}, "engines": {"node": ">=18"}}