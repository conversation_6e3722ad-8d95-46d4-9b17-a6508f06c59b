{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "declaration": true, "declarationMap": true, "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}